<script setup lang="ts">
import LineLogin from '@/components/LineLogin.vue'
</script>

<template>
  <div class="login-container">
    <div class="login-card">
      <div class="content-wrapper">
        <div class="logo-container">
          <div class="line-logo">LINE</div>
        </div>
        <h1>Welcome to Line Login</h1>
        <p class="subtitle">Connect with your Line account to get started</p>
        <div class="features">
          <div class="feature">
            <span class="feature-icon">🔐</span>
            <span>Secure Authentication</span>
          </div>
          <div class="feature">
            <span class="feature-icon">👤</span>
            <span>Profile Access</span>
          </div>
          <div class="feature">
            <span class="feature-icon">🔄</span>
            <span>Easy Integration</span>
          </div>
        </div>
        <LineLogin />
        <p class="terms">By continuing, you agree to our Terms of Service and Privacy Policy</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.login-container {
  min-height: 100vh;
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #00b900 0%, #00c3ff 100%);
  padding: 20px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 0;
}

.login-card {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 480px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.content-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.logo-container {
  margin-bottom: 24px;
  display: flex;
  justify-content: center;
}

.line-logo {
  font-size: 2.5rem;
  font-weight: bold;
  color: #00b900;
  letter-spacing: 2px;
}

h1 {
  color: #333;
  font-size: 2rem;
  margin-bottom: 12px;
  font-weight: 600;
  text-align: center;
}

.subtitle {
  color: #666;
  margin-bottom: 32px;
  font-size: 1.1rem;
  text-align: center;
}

.features {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 32px;
  width: 100%;
  align-items: center;
}

.feature {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #555;
  font-size: 1rem;
  justify-content: center;
}

.feature-icon {
  font-size: 1.5rem;
}

.terms {
  margin-top: 24px;
  color: #888;
  font-size: 0.9rem;
  text-align: center;
}

@media (max-width: 480px) {
  .login-card {
    padding: 24px;
  }

  h1 {
    font-size: 1.75rem;
  }

  .subtitle {
    font-size: 1rem;
  }
}
</style>

<style>
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}
</style> 