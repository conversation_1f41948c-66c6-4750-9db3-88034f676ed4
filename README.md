# Line OAuth2 Login Application

This is a full-stack application that implements Line OAuth2 login functionality.

## Tech Stack

### Frontend
- Vue.js 3
- TypeScript
- TailwindCSS
- Vite (Build Tool)

### Backend
- Go 1.24
- GORM (ORM)
- Chi (Router)
- PostgreSQL

## Features
- Line OAuth2 Login Integration
- Persistent Login Session
- Secure Authentication Flow

## Project Structure
```
.
├── frontend/           # Vue.js frontend application
│   ├── src/           # Source files
│   ├── public/        # Static assets
│   └── vite.config.ts # Vite configuration
├── backend/           # Go backend application
│   ├── api/          # API handlers and routes
│   ├── config/       # Configuration files
│   ├── models/       # Database models
│   ├── services/     # Business logic
│   └── utils/        # Utility functions
└── README.md
```

## Setup Instructions

### Backend Setup
1. Navigate to the backend directory: `cd backend`
2. Install dependencies: `go mod tidy`
3. Set up environment variables (see below)
4. Run the server: `go run main.go`

### Frontend Setup
1. Navigate to the frontend directory: `cd frontend`
2. Install dependencies: `npm install`
3. Run the development server: `npm run dev`

## Environment Variables
Create a `.env` file in the backend directory with the following variables:
```
LINE_CHANNEL_ID=your_channel_id
LINE_CHANNEL_SECRET=your_channel_secret
LINE_CALLBACK_URL=http://localhost:3000/api/auth/callback
DATABASE_URL=postgres://username:password@localhost:5432/dbname
```

## Development
- Backend runs on: http://localhost:8080
- Frontend runs on: http://localhost:3000

## Development Notes
- Use `mkdir -p backend/{api,config,models,services,utils}` to create multiple directories at once
- Make sure to have PostgreSQL running locally before starting the backend
- The frontend uses Vite for fast development and building
- The backend uses Chi router for clean and efficient routing

## My notes:
mkdir -p backend/{api,config,models,services,utils} => tạo nhiều thư mục cùng lúc

